#!/usr/bin/env python3
"""
YOLO11 训练环境安装脚本
自动安装所需的依赖包
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n{'='*50}")
    if description:
        print(f"正在执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✓ 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    print("✓ Python版本符合要求")
    return True

def check_pip():
    """检查pip是否可用"""
    print("\n检查pip...")
    try:
        import pip
        print("✓ pip可用")
        return True
    except ImportError:
        print("❌ pip不可用，请先安装pip")
        return False

def install_pytorch():
    """安装PyTorch"""
    print("\n检查PyTorch...")
    
    try:
        import torch
        print(f"✓ PyTorch已安装，版本: {torch.__version__}")
        return True
    except ImportError:
        print("PyTorch未安装，正在安装...")
        
        # 检测CUDA
        cuda_available = False
        try:
            result = subprocess.run("nvidia-smi", shell=True, capture_output=True)
            if result.returncode == 0:
                cuda_available = True
                print("✓ 检测到NVIDIA GPU")
            else:
                print("未检测到NVIDIA GPU，将安装CPU版本")
        except:
            print("未检测到NVIDIA GPU，将安装CPU版本")
        
        # 安装PyTorch
        if cuda_available:
            command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        else:
            command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        
        return run_command(command, "安装PyTorch")

def install_ultralytics():
    """安装Ultralytics"""
    print("\n检查Ultralytics...")
    
    try:
        import ultralytics
        print(f"✓ Ultralytics已安装，版本: {ultralytics.__version__}")
        return True
    except ImportError:
        print("Ultralytics未安装，正在安装...")
        return run_command("pip install ultralytics", "安装Ultralytics")

def install_requirements():
    """安装requirements.txt中的依赖"""
    print("\n安装其他依赖...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    return run_command("pip install -r requirements.txt", "安装requirements.txt中的依赖")

def verify_installation():
    """验证安装"""
    print("\n验证安装...")
    
    packages_to_check = [
        ("torch", "PyTorch"),
        ("ultralytics", "Ultralytics"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("yaml", "PyYAML"),
        ("PIL", "Pillow")
    ]
    
    all_good = True
    for package, name in packages_to_check:
        try:
            __import__(package)
            print(f"✓ {name}")
        except ImportError:
            print(f"❌ {name} 未正确安装")
            all_good = False
    
    return all_good

def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
# 测试YOLO11安装
from ultralytics import YOLO
import torch

print("YOLO11安装测试")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

# 测试加载模型
try:
    model = YOLO('yolo11n.pt')
    print("✓ YOLO11模型加载成功")
    
    # 测试预测
    results = model.predict('https://ultralytics.com/images/bus.jpg', verbose=False)
    print(f"✓ 预测成功，检测到 {len(results[0].boxes)} 个目标")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")

print("安装测试完成!")
"""
    
    with open("test_installation.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✓ 测试脚本已创建: test_installation.py")

def main():
    """主安装流程"""
    print("YOLO11训练环境安装脚本")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查pip
    if not check_pip():
        return False
    
    # 升级pip
    print("\n升级pip...")
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 安装PyTorch
    if not install_pytorch():
        print("❌ PyTorch安装失败")
        return False
    
    # 安装Ultralytics
    if not install_ultralytics():
        print("❌ Ultralytics安装失败")
        return False
    
    # 安装其他依赖
    if not install_requirements():
        print("⚠️ 部分依赖安装失败，但核心功能应该可用")
    
    # 验证安装
    if verify_installation():
        print("\n🎉 安装完成！")
        
        # 创建测试脚本
        create_test_script()
        
        print("\n下一步:")
        print("1. 运行测试: python test_installation.py")
        print("2. 创建配置: python main.py --mode create-config")
        print("3. 开始训练: python main.py --mode train")
        
        return True
    else:
        print("\n❌ 安装验证失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
