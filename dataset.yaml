# 数据集配置文件示例
# 请根据您的实际数据集修改以下配置

# 数据集根目录
path: ./dataset

# 训练、验证、测试数据目录（相对于path）
train: images/train
val: images/val
# test: images/test  # 可选

# 类别数量
nc: 1

# 类别名称列表（按索引顺序）
names:
  0: guiti
  1: bicycle
  2: car
  3: motorcycle
  4: airplane
  5: bus
  6: train
  7: truck
  8: boat
  9: traffic light
  10: fire hydrant
  11: stop sign
  12: parking meter
  13: bench
  14: bird
  15: cat
  16: dog
  17: horse
  18: sheep
  19: cow
  20: elephant
  21: bear
  22: zebra
  23: giraffe
  24: backpack
  25: umbrella
  26: handbag
  27: tie
  28: suitcase
  29: frisbee
  30: skis
  31: snowboard
  32: sports ball
  33: kite
  34: baseball bat
  35: baseball glove
  36: skateboard
  37: surfboard
  38: tennis racket
  39: bottle
  40: wine glass
  41: cup
  42: fork
  43: knife
  44: spoon
  45: bowl
  46: banana
  47: apple
  48: sandwich
  49: orange
  50: broccoli
  51: carrot
  52: hot dog
  53: pizza
  54: donut
  55: cake
  56: chair
  57: couch
  58: potted plant
  59: bed
  60: dining table
  61: toilet
  62: tv
  63: laptop
  64: mouse
  65: remote
  66: keyboard
  67: cell phone
  68: microwave
  69: oven
  70: toaster
  71: sink
  72: refrigerator
  73: book
  74: clock
  75: vase
  76: scissors
  77: teddy bear
  78: hair drier
  79: toothbrush

# 数据集信息（可选）
download: |
  # 下载数据集的脚本或说明
  # 例如：
  # wget https://example.com/dataset.zip
  # unzip dataset.zip

# 数据集描述（可选）
description: |
  这是一个示例数据集配置文件。
  请根据您的实际数据集修改相应的配置。
  
  数据集目录结构应该如下：
  dataset/
  ├── images/
  │   ├── train/
  │   │   ├── image1.jpg
  │   │   ├── image2.jpg
  │   │   └── ...
  │   ├── val/
  │   │   ├── image1.jpg
  │   │   ├── image2.jpg
  │   │   └── ...
  │   └── test/  (可选)
  │       ├── image1.jpg
  │       ├── image2.jpg
  │       └── ...
  └── labels/
      ├── train/
      │   ├── image1.txt
      │   ├── image2.txt
      │   └── ...
      ├── val/
      │   ├── image1.txt
      │   ├── image2.txt
      │   └── ...
      └── test/  (可选)
          ├── image1.txt
          ├── image2.txt
          └── ...

# 标注格式说明
# YOLO格式的标注文件(.txt)每行包含一个目标：
# class_id center_x center_y width height
# 其中坐标值都是相对于图像尺寸的归一化值(0-1)
