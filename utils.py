#!/usr/bin/env python3
"""
YOLO11 训练工具函数
"""

import os
import json
import yaml
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)


def check_dataset_structure(dataset_path: str) -> bool:
    """
    检查数据集目录结构是否正确
    
    Args:
        dataset_path: 数据集根目录路径
        
    Returns:
        bool: 目录结构是否正确
    """
    dataset_path = Path(dataset_path)
    
    required_dirs = [
        dataset_path / "images" / "train",
        dataset_path / "images" / "val",
        dataset_path / "labels" / "train",
        dataset_path / "labels" / "val"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not dir_path.exists():
            missing_dirs.append(str(dir_path))
    
    if missing_dirs:
        logger.error(f"缺少以下目录: {missing_dirs}")
        return False
    
    logger.info("数据集目录结构检查通过")
    return True


def count_dataset_files(dataset_path: str) -> Dict[str, int]:
    """
    统计数据集文件数量
    
    Args:
        dataset_path: 数据集根目录路径
        
    Returns:
        Dict: 包含各部分文件数量的字典
    """
    dataset_path = Path(dataset_path)
    
    counts = {
        'train_images': 0,
        'train_labels': 0,
        'val_images': 0,
        'val_labels': 0,
        'test_images': 0,
        'test_labels': 0
    }
    
    # 统计训练集
    train_img_dir = dataset_path / "images" / "train"
    if train_img_dir.exists():
        counts['train_images'] = len(list(train_img_dir.glob("*.jpg")) + 
                                     list(train_img_dir.glob("*.png")) + 
                                     list(train_img_dir.glob("*.jpeg")))
    
    train_label_dir = dataset_path / "labels" / "train"
    if train_label_dir.exists():
        counts['train_labels'] = len(list(train_label_dir.glob("*.txt")))
    
    # 统计验证集
    val_img_dir = dataset_path / "images" / "val"
    if val_img_dir.exists():
        counts['val_images'] = len(list(val_img_dir.glob("*.jpg")) + 
                                   list(val_img_dir.glob("*.png")) + 
                                   list(val_img_dir.glob("*.jpeg")))
    
    val_label_dir = dataset_path / "labels" / "val"
    if val_label_dir.exists():
        counts['val_labels'] = len(list(val_label_dir.glob("*.txt")))
    
    # 统计测试集（可选）
    test_img_dir = dataset_path / "images" / "test"
    if test_img_dir.exists():
        counts['test_images'] = len(list(test_img_dir.glob("*.jpg")) + 
                                    list(test_img_dir.glob("*.png")) + 
                                    list(test_img_dir.glob("*.jpeg")))
    
    test_label_dir = dataset_path / "labels" / "test"
    if test_label_dir.exists():
        counts['test_labels'] = len(list(test_label_dir.glob("*.txt")))
    
    return counts


def validate_dataset_consistency(dataset_path: str) -> bool:
    """
    验证数据集一致性（图像和标签文件是否匹配）
    
    Args:
        dataset_path: 数据集根目录路径
        
    Returns:
        bool: 数据集是否一致
    """
    dataset_path = Path(dataset_path)
    
    splits = ['train', 'val', 'test']
    all_consistent = True
    
    for split in splits:
        img_dir = dataset_path / "images" / split
        label_dir = dataset_path / "labels" / split
        
        if not img_dir.exists() or not label_dir.exists():
            continue
        
        # 获取图像文件名（不含扩展名）
        img_files = set()
        for ext in ['*.jpg', '*.png', '*.jpeg']:
            img_files.update([f.stem for f in img_dir.glob(ext)])
        
        # 获取标签文件名（不含扩展名）
        label_files = set([f.stem for f in label_dir.glob('*.txt')])
        
        # 检查是否匹配
        missing_labels = img_files - label_files
        missing_images = label_files - img_files
        
        if missing_labels:
            logger.warning(f"{split}集中缺少标签文件: {list(missing_labels)[:10]}...")
            all_consistent = False
        
        if missing_images:
            logger.warning(f"{split}集中缺少图像文件: {list(missing_images)[:10]}...")
            all_consistent = False
        
        if not missing_labels and not missing_images:
            logger.info(f"{split}集图像和标签文件匹配正确")
    
    return all_consistent


def visualize_annotations(image_path: str, label_path: str, class_names: List[str], 
                         save_path: Optional[str] = None) -> np.ndarray:
    """
    可视化图像和标注
    
    Args:
        image_path: 图像文件路径
        label_path: 标签文件路径
        class_names: 类别名称列表
        save_path: 保存路径（可选）
        
    Returns:
        np.ndarray: 可视化后的图像
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    h, w = image.shape[:2]
    
    # 读取标签
    if not os.path.exists(label_path):
        logger.warning(f"标签文件不存在: {label_path}")
        return image
    
    with open(label_path, 'r') as f:
        lines = f.readlines()
    
    # 绘制边界框
    for line in lines:
        parts = line.strip().split()
        if len(parts) < 5:
            continue
        
        class_id = int(parts[0])
        center_x = float(parts[1]) * w
        center_y = float(parts[2]) * h
        width = float(parts[3]) * w
        height = float(parts[4]) * h
        
        # 计算边界框坐标
        x1 = int(center_x - width / 2)
        y1 = int(center_y - height / 2)
        x2 = int(center_x + width / 2)
        y2 = int(center_y + height / 2)
        
        # 绘制边界框
        color = (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255))
        cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
        
        # 绘制类别标签
        if class_id < len(class_names):
            label = class_names[class_id]
            cv2.putText(image, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # 保存图像
    if save_path:
        cv2.imwrite(save_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    
    return image


def create_dataset_report(dataset_path: str, output_path: str = "dataset_report.html"):
    """
    创建数据集报告
    
    Args:
        dataset_path: 数据集路径
        output_path: 报告输出路径
    """
    dataset_path = Path(dataset_path)
    
    # 检查数据集结构
    structure_ok = check_dataset_structure(dataset_path)
    
    # 统计文件数量
    file_counts = count_dataset_files(dataset_path)
    
    # 验证一致性
    consistency_ok = validate_dataset_consistency(dataset_path)
    
    # 生成HTML报告
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>数据集报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ color: #333; border-bottom: 2px solid #333; }}
            .section {{ margin: 20px 0; }}
            .status-ok {{ color: green; }}
            .status-error {{ color: red; }}
            .status-warning {{ color: orange; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <h1 class="header">数据集报告</h1>
        
        <div class="section">
            <h2>基本信息</h2>
            <p><strong>数据集路径:</strong> {dataset_path}</p>
            <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="section">
            <h2>目录结构检查</h2>
            <p class="{'status-ok' if structure_ok else 'status-error'}">
                {'✓ 目录结构正确' if structure_ok else '✗ 目录结构有问题'}
            </p>
        </div>
        
        <div class="section">
            <h2>文件统计</h2>
            <table>
                <tr><th>数据集</th><th>图像数量</th><th>标签数量</th></tr>
                <tr><td>训练集</td><td>{file_counts['train_images']}</td><td>{file_counts['train_labels']}</td></tr>
                <tr><td>验证集</td><td>{file_counts['val_images']}</td><td>{file_counts['val_labels']}</td></tr>
                <tr><td>测试集</td><td>{file_counts['test_images']}</td><td>{file_counts['test_labels']}</td></tr>
            </table>
        </div>
        
        <div class="section">
            <h2>数据一致性检查</h2>
            <p class="{'status-ok' if consistency_ok else 'status-warning'}">
                {'✓ 图像和标签文件匹配' if consistency_ok else '⚠ 存在不匹配的文件'}
            </p>
        </div>
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"数据集报告已生成: {output_path}")


def split_dataset(source_dir: str, output_dir: str, train_ratio: float = 0.8, 
                  val_ratio: float = 0.1, test_ratio: float = 0.1, seed: int = 42):
    """
    将数据集按比例分割为训练集、验证集和测试集
    
    Args:
        source_dir: 源数据目录
        output_dir: 输出目录
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        seed: 随机种子
    """
    import random
    from datetime import datetime
    
    random.seed(seed)
    np.random.seed(seed)
    
    source_dir = Path(source_dir)
    output_dir = Path(output_dir)
    
    # 创建输出目录
    for split in ['train', 'val', 'test']:
        (output_dir / 'images' / split).mkdir(parents=True, exist_ok=True)
        (output_dir / 'labels' / split).mkdir(parents=True, exist_ok=True)
    
    # 获取所有图像文件
    image_files = []
    for ext in ['*.jpg', '*.png', '*.jpeg']:
        image_files.extend(list((source_dir / 'images').glob(ext)))
    
    # 随机打乱
    random.shuffle(image_files)
    
    # 计算分割点
    total = len(image_files)
    train_end = int(total * train_ratio)
    val_end = train_end + int(total * val_ratio)
    
    # 分割文件
    splits = {
        'train': image_files[:train_end],
        'val': image_files[train_end:val_end],
        'test': image_files[val_end:]
    }
    
    # 复制文件
    for split, files in splits.items():
        for img_file in files:
            # 复制图像
            dst_img = output_dir / 'images' / split / img_file.name
            shutil.copy2(img_file, dst_img)
            
            # 复制标签
            label_file = source_dir / 'labels' / f"{img_file.stem}.txt"
            if label_file.exists():
                dst_label = output_dir / 'labels' / split / f"{img_file.stem}.txt"
                shutil.copy2(label_file, dst_label)
    
    logger.info(f"数据集分割完成:")
    logger.info(f"  训练集: {len(splits['train'])} 个文件")
    logger.info(f"  验证集: {len(splits['val'])} 个文件")
    logger.info(f"  测试集: {len(splits['test'])} 个文件")


if __name__ == "__main__":
    # 示例用法
    dataset_path = "./dataset"
    
    # 检查数据集
    if check_dataset_structure(dataset_path):
        counts = count_dataset_files(dataset_path)
        print("文件统计:", counts)
        
        validate_dataset_consistency(dataset_path)
        create_dataset_report(dataset_path)
