#!/usr/bin/env python3
"""
YOLO11训练脚本完整测试
测试所有功能是否正常工作
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试主模块
        from main import YOLO11Trainer, create_sample_config, create_sample_dataset_config
        print("  ✓ main.py 导入成功")
        
        # 测试工具模块
        from utils import check_dataset_structure, count_dataset_files
        print("  ✓ utils.py 导入成功")
        
        return True
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_config_creation():
    """测试配置文件创建"""
    print("\n📝 测试配置文件创建...")
    
    try:
        from main import create_sample_config, create_sample_dataset_config
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            os.chdir(temp_dir)
            
            # 测试创建配置文件
            create_sample_config()
            if Path("config.yaml").exists():
                print("  ✓ config.yaml 创建成功")
            else:
                print("  ❌ config.yaml 创建失败")
                return False
            
            # 测试创建数据集配置
            create_sample_dataset_config()
            if Path("dataset.yaml").exists():
                print("  ✓ dataset.yaml 创建成功")
            else:
                print("  ❌ dataset.yaml 创建失败")
                return False
        
        return True
    except Exception as e:
        print(f"  ❌ 配置文件创建失败: {e}")
        return False

def test_trainer_initialization():
    """测试训练器初始化"""
    print("\n🏗️ 测试训练器初始化...")
    
    try:
        from main import YOLO11Trainer
        
        # 使用默认配置初始化
        trainer = YOLO11Trainer("nonexistent_config.yaml")  # 应该使用默认配置
        
        if trainer.config is not None:
            print("  ✓ 训练器初始化成功")
            print(f"  ✓ 设备: {trainer.device}")
            return True
        else:
            print("  ❌ 训练器配置为空")
            return False
            
    except Exception as e:
        print(f"  ❌ 训练器初始化失败: {e}")
        return False

def test_utils_functions():
    """测试工具函数"""
    print("\n🛠️ 测试工具函数...")
    
    try:
        from utils import check_dataset_structure, count_dataset_files
        
        # 创建临时数据集结构
        with tempfile.TemporaryDirectory() as temp_dir:
            dataset_path = Path(temp_dir) / "test_dataset"
            
            # 创建目录结构
            dirs_to_create = [
                dataset_path / "images" / "train",
                dataset_path / "images" / "val",
                dataset_path / "labels" / "train",
                dataset_path / "labels" / "val"
            ]
            
            for dir_path in dirs_to_create:
                dir_path.mkdir(parents=True, exist_ok=True)
            
            # 创建一些测试文件
            (dataset_path / "images" / "train" / "test1.jpg").touch()
            (dataset_path / "images" / "train" / "test2.jpg").touch()
            (dataset_path / "labels" / "train" / "test1.txt").touch()
            (dataset_path / "labels" / "train" / "test2.txt").touch()
            
            # 测试检查函数
            if check_dataset_structure(str(dataset_path)):
                print("  ✓ 数据集结构检查功能正常")
            else:
                print("  ❌ 数据集结构检查失败")
                return False
            
            # 测试统计函数
            counts = count_dataset_files(str(dataset_path))
            if counts['train_images'] == 2 and counts['train_labels'] == 2:
                print("  ✓ 文件统计功能正常")
            else:
                print(f"  ❌ 文件统计错误: {counts}")
                return False
        
        return True
    except Exception as e:
        print(f"  ❌ 工具函数测试失败: {e}")
        return False

def test_command_line_interface():
    """测试命令行接口"""
    print("\n💻 测试命令行接口...")
    
    try:
        import subprocess
        import sys
        
        # 测试帮助信息
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "YOLO11 训练脚本" in result.stdout:
            print("  ✓ 命令行帮助正常")
        else:
            print("  ❌ 命令行帮助失败")
            return False
        
        # 测试创建配置
        result = subprocess.run([sys.executable, "main.py", "--mode", "create-config"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("  ✓ 创建配置命令正常")
        else:
            print(f"  ❌ 创建配置命令失败: {result.stderr}")
            return False
        
        return True
    except Exception as e:
        print(f"  ❌ 命令行接口测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 测试依赖包...")
    
    required_packages = [
        ("yaml", "PyYAML"),
        ("pathlib", "pathlib"),
        ("logging", "logging"),
        ("json", "json")
    ]
    
    optional_packages = [
        ("ultralytics", "Ultralytics"),
        ("torch", "PyTorch"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy")
    ]
    
    all_required_ok = True
    
    # 检查必需包
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {name}")
        except ImportError:
            print(f"  ❌ {name} - 必需包缺失")
            all_required_ok = False
    
    # 检查可选包
    optional_ok = 0
    for package, name in optional_packages:
        try:
            __import__(package)
            print(f"  ✓ {name}")
            optional_ok += 1
        except ImportError:
            print(f"  ⚠️ {name} - 可选包未安装")
    
    if all_required_ok:
        print(f"  ✓ 所有必需依赖已安装")
        print(f"  ℹ️ 可选依赖: {optional_ok}/{len(optional_packages)} 已安装")
        return True
    else:
        print("  ❌ 缺少必需依赖")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_files = [
        "main.py",
        "config.yaml", 
        "dataset.yaml",
        "utils.py",
        "requirements.txt",
        "README.md"
    ]
    
    optional_files = [
        "install.py",
        "quick_start.py",
        "train_example.py",
        "run.bat",
        "run.sh"
    ]
    
    missing_required = []
    for file in required_files:
        if not Path(file).exists():
            missing_required.append(file)
        else:
            print(f"  ✓ {file}")
    
    if missing_required:
        print(f"  ❌ 缺少必需文件: {missing_required}")
        return False
    
    # 检查可选文件
    optional_count = 0
    for file in optional_files:
        if Path(file).exists():
            print(f"  ✓ {file}")
            optional_count += 1
        else:
            print(f"  ⚠️ {file} - 可选文件")
    
    print(f"  ✓ 所有必需文件存在")
    print(f"  ℹ️ 可选文件: {optional_count}/{len(optional_files)} 存在")
    return True

def main():
    """运行所有测试"""
    print("🧪 YOLO11训练脚本完整测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("依赖包", test_dependencies),
        ("模块导入", test_imports),
        ("配置文件创建", test_config_creation),
        ("训练器初始化", test_trainer_initialization),
        ("工具函数", test_utils_functions),
        ("命令行接口", test_command_line_interface)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常使用。")
        print("\n🚀 下一步:")
        print("1. 运行 python install.py 安装依赖")
        print("2. 运行 python quick_start.py 开始使用")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，项目基本可用。")
        print("请检查失败的测试项目。")
    else:
        print("❌ 多个测试失败，请检查项目配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
