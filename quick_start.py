#!/usr/bin/env python3
"""
YOLO11 快速开始脚本
帮助用户快速开始使用YOLO11训练
"""

import os
import sys
import yaml
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    YOLO11 快速开始向导                        ║
    ║                                                              ║
    ║  这个脚本将帮助您快速设置和开始YOLO11训练                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        ("ultralytics", "Ultralytics YOLO"),
        ("torch", "PyTorch"),
        ("yaml", "PyYAML"),
        ("cv2", "OpenCV")
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {name}")
        except ImportError:
            print(f"  ❌ {name} - 未安装")
            missing_packages.append(name)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("  python install.py")
        print("或者:")
        print("  pip install ultralytics torch opencv-python PyYAML")
        return False
    
    print("✓ 所有依赖已安装")
    return True

def choose_task():
    """选择任务类型"""
    print("\n📋 选择您要训练的任务类型:")
    tasks = {
        "1": ("detect", "目标检测", "yolo11n.pt"),
        "2": ("segment", "实例分割", "yolo11n-seg.pt"),
        "3": ("classify", "图像分类", "yolo11n-cls.pt"),
        "4": ("pose", "姿态估计", "yolo11n-pose.pt"),
        "5": ("obb", "旋转目标检测", "yolo11n-obb.pt")
    }
    
    for key, (task, desc, model) in tasks.items():
        print(f"  {key}. {desc} ({task})")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        if choice in tasks:
            task, desc, model = tasks[choice]
            print(f"✓ 已选择: {desc}")
            return task, model
        else:
            print("❌ 无效选择，请输入1-5")

def choose_model_size():
    """选择模型大小"""
    print("\n🏗️ 选择模型大小:")
    sizes = {
        "1": ("n", "Nano - 最小最快"),
        "2": ("s", "Small - 小型"),
        "3": ("m", "Medium - 中型"),
        "4": ("l", "Large - 大型"),
        "5": ("x", "Extra Large - 超大型")
    }
    
    for key, (size, desc) in sizes.items():
        print(f"  {key}. {desc}")
    
    while True:
        choice = input("\n请选择 (1-5, 推荐新手选择1或2): ").strip()
        if choice in sizes:
            size, desc = sizes[choice]
            print(f"✓ 已选择: {desc}")
            return size
        else:
            print("❌ 无效选择，请输入1-5")

def get_dataset_info():
    """获取数据集信息"""
    print("\n📁 数据集配置:")
    
    # 检查是否有现有的数据集配置
    if Path("dataset.yaml").exists():
        use_existing = input("发现现有的dataset.yaml，是否使用? (y/n): ").strip().lower()
        if use_existing in ['y', 'yes', '']:
            return "dataset.yaml"
    
    print("\n选择数据集:")
    print("  1. 使用COCO8示例数据集 (推荐新手)")
    print("  2. 使用自定义数据集")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        return "coco8.yaml"
    elif choice == "2":
        dataset_path = input("请输入数据集配置文件路径: ").strip()
        if not Path(dataset_path).exists():
            print(f"❌ 文件不存在: {dataset_path}")
            print("将使用默认的dataset.yaml")
            return "dataset.yaml"
        return dataset_path
    else:
        print("使用默认配置")
        return "coco8.yaml"

def get_training_params():
    """获取训练参数"""
    print("\n⚙️ 训练参数配置:")
    
    # 训练轮数
    while True:
        try:
            epochs = input("训练轮数 (默认100): ").strip()
            epochs = int(epochs) if epochs else 100
            if epochs > 0:
                break
            else:
                print("❌ 训练轮数必须大于0")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 批次大小
    while True:
        try:
            batch_size = input("批次大小 (默认16): ").strip()
            batch_size = int(batch_size) if batch_size else 16
            if batch_size > 0:
                break
            else:
                print("❌ 批次大小必须大于0")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 图像尺寸
    while True:
        try:
            imgsz = input("输入图像尺寸 (默认640): ").strip()
            imgsz = int(imgsz) if imgsz else 640
            if imgsz > 0:
                break
            else:
                print("❌ 图像尺寸必须大于0")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 设备选择
    print("\n设备选择:")
    print("  1. 自动选择 (推荐)")
    print("  2. 强制使用CPU")
    print("  3. 强制使用GPU")
    
    device_choice = input("请选择 (1-3): ").strip()
    device_map = {"1": "auto", "2": "cpu", "3": "cuda"}
    device = device_map.get(device_choice, "auto")
    
    return {
        "epochs": epochs,
        "batch_size": batch_size,
        "imgsz": imgsz,
        "device": device
    }

def create_config(task, model_name, dataset_path, params):
    """创建配置文件"""
    print("\n📝 创建配置文件...")
    
    config = {
        "model": {
            "name": model_name,
            "task": task
        },
        "data": {
            "path": dataset_path,
            "imgsz": params["imgsz"],
            "batch_size": params["batch_size"],
            "workers": 4
        },
        "training": {
            "epochs": params["epochs"],
            "patience": 50,
            "val": True,
            "plots": True,
            "verbose": True,
            "amp": True,
            "resume": False
        },
        "optimizer": {
            "optimizer": "auto",
            "lr0": 0.01,
            "momentum": 0.937,
            "weight_decay": 0.0005
        },
        "device": params["device"],
        "project": f"yolo11_{task}",
        "wandb": {
            "enabled": False
        }
    }
    
    config_path = "quick_start_config.yaml"
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✓ 配置文件已创建: {config_path}")
    return config_path

def show_summary(task, model_name, dataset_path, params, config_path):
    """显示配置摘要"""
    print("\n" + "="*60)
    print("📋 配置摘要")
    print("="*60)
    print(f"任务类型: {task}")
    print(f"模型: {model_name}")
    print(f"数据集: {dataset_path}")
    print(f"训练轮数: {params['epochs']}")
    print(f"批次大小: {params['batch_size']}")
    print(f"图像尺寸: {params['imgsz']}")
    print(f"设备: {params['device']}")
    print(f"配置文件: {config_path}")
    print("="*60)

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    print("\n🚀 开始配置...")
    
    # 选择任务和模型
    task, base_model = choose_task()
    size = choose_model_size()
    
    # 构建完整模型名
    if task == "detect":
        model_name = f"yolo11{size}.pt"
    else:
        model_name = f"yolo11{size}-{task}.pt"
    
    # 获取数据集信息
    dataset_path = get_dataset_info()
    
    # 获取训练参数
    params = get_training_params()
    
    # 创建配置文件
    config_path = create_config(task, model_name, dataset_path, params)
    
    # 显示摘要
    show_summary(task, model_name, dataset_path, params, config_path)
    
    # 询问是否立即开始训练
    print("\n🎯 准备就绪！")
    start_training = input("是否立即开始训练? (y/n): ").strip().lower()
    
    if start_training in ['y', 'yes', '']:
        print("\n🚀 开始训练...")
        print(f"运行命令: python main.py --config {config_path}")
        
        # 导入并运行训练
        try:
            from main import YOLO11Trainer
            trainer = YOLO11Trainer(config_path)
            trainer.train()
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            print(f"您可以手动运行: python main.py --config {config_path}")
    else:
        print(f"\n📝 配置已保存到: {config_path}")
        print("您可以稍后运行以下命令开始训练:")
        print(f"  python main.py --config {config_path}")
    
    print("\n✨ 快速开始向导完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请检查错误信息或寻求帮助")
