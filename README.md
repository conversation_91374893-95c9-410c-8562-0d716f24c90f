# YOLO11 训练脚本

这是一个完整的YOLO11训练脚本，支持目标检测、实例分割、姿态估计、分类和旋转目标检测任务。

## 功能特性

- 🚀 支持YOLO11所有任务类型（检测、分割、分类、姿态估计、旋转目标检测）
- ⚙️ 灵活的配置文件系统
- 📊 集成Weights & Biases日志记录
- 🔧 完整的命令行接口
- 📈 自动生成训练报告
- 🛠️ 数据集验证和可视化工具
- 💾 支持模型导出到多种格式

## 安装依赖

```bash
pip install ultralytics
pip install wandb  # 可选，用于实验跟踪
pip install opencv-python
pip install matplotlib
pip install pillow
```

## 快速开始

### 1. 创建配置文件

```bash
python main.py --mode create-config
```

这将创建一个 `config.yaml` 配置文件，您可以根据需要修改。

### 2. 创建数据集配置

```bash
python main.py --mode create-dataset
```

这将创建一个 `dataset.yaml` 数据集配置文件模板。

### 3. 准备数据集

确保您的数据集目录结构如下：

```
dataset/
├── images/
│   ├── train/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   ├── val/
│   │   ├── image1.jpg
│   │   ├── image2.jpg
│   │   └── ...
│   └── test/  (可选)
│       ├── image1.jpg
│       └── ...
└── labels/
    ├── train/
    │   ├── image1.txt
    │   ├── image2.txt
    │   └── ...
    ├── val/
    │   ├── image1.txt
    │   ├── image2.txt
    │   └── ...
    └── test/  (可选)
        ├── image1.txt
        └── ...
```

### 4. 开始训练

```bash
# 使用配置文件训练
python main.py --mode train --config config.yaml

# 或者使用命令行参数
python main.py --mode train --data dataset.yaml --epochs 100 --batch-size 16
```

## 使用方法

### 训练模式

```bash
# 基础训练
python main.py --mode train

# 指定参数训练
python main.py --mode train --data dataset.yaml --epochs 100 --batch-size 16 --device cuda

# 恢复训练
python main.py --mode train --resume
```

### 验证模式

```bash
# 验证训练好的模型
python main.py --mode val --model runs/train/exp/weights/best.pt --data dataset.yaml
```

### 预测模式

```bash
# 预测单张图像
python main.py --mode predict --model runs/train/exp/weights/best.pt --source image.jpg

# 预测整个目录
python main.py --mode predict --model runs/train/exp/weights/best.pt --source images/

# 预测视频
python main.py --mode predict --model runs/train/exp/weights/best.pt --source video.mp4
```

### 模型导出

```bash
# 导出为ONNX格式
python main.py --mode export --model runs/train/exp/weights/best.pt --format onnx

# 导出为TensorRT格式
python main.py --mode export --model runs/train/exp/weights/best.pt --format engine
```

## 配置文件说明

### 模型配置

```yaml
model:
  name: yolo11n.pt  # 模型大小: n, s, m, l, x
  task: detect      # 任务类型: detect, segment, classify, pose, obb
```

### 数据配置

```yaml
data:
  path: dataset.yaml  # 数据集配置文件
  imgsz: 640         # 输入图像尺寸
  batch_size: 16     # 批次大小
  workers: 8         # 数据加载器进程数
```

### 训练配置

```yaml
training:
  epochs: 100        # 训练轮数
  patience: 50       # 早停耐心值
  amp: true          # 自动混合精度
  resume: false      # 恢复训练
```

### 优化器配置

```yaml
optimizer:
  optimizer: auto    # 优化器类型
  lr0: 0.01         # 初始学习率
  momentum: 0.937   # 动量
  weight_decay: 0.0005  # 权重衰减
```

## 支持的模型

### 目标检测
- yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt

### 实例分割
- yolo11n-seg.pt, yolo11s-seg.pt, yolo11m-seg.pt, yolo11l-seg.pt, yolo11x-seg.pt

### 图像分类
- yolo11n-cls.pt, yolo11s-cls.pt, yolo11m-cls.pt, yolo11l-cls.pt, yolo11x-cls.pt

### 姿态估计
- yolo11n-pose.pt, yolo11s-pose.pt, yolo11m-pose.pt, yolo11l-pose.pt, yolo11x-pose.pt

### 旋转目标检测
- yolo11n-obb.pt, yolo11s-obb.pt, yolo11m-obb.pt, yolo11l-obb.pt, yolo11x-obb.pt

## 数据集工具

### 检查数据集结构

```python
from utils import check_dataset_structure, count_dataset_files

# 检查目录结构
check_dataset_structure("./dataset")

# 统计文件数量
counts = count_dataset_files("./dataset")
print(counts)
```

### 生成数据集报告

```python
from utils import create_dataset_report

create_dataset_report("./dataset", "report.html")
```

### 可视化标注

```python
from utils import visualize_annotations

# 可视化单张图像的标注
image = visualize_annotations(
    "dataset/images/train/image1.jpg",
    "dataset/labels/train/image1.txt",
    class_names=["person", "car", "bike"],
    save_path="annotated_image.jpg"
)
```

## Weights & Biases 集成

在配置文件中启用WandB：

```yaml
wandb:
  enabled: true
  project: my-yolo11-project
  name: experiment-1
  tags: ["yolo11", "detection"]
  notes: "First training run"
```

## 输出文件

训练完成后，会在 `runs/train/exp/` 目录下生成：

- `weights/best.pt` - 最佳模型权重
- `weights/last.pt` - 最后一轮权重
- `results.png` - 训练曲线图
- `confusion_matrix.png` - 混淆矩阵
- `val_batch*.jpg` - 验证批次可视化
- `training_summary.json` - 训练摘要

## 常见问题

### 1. 内存不足
- 减小 `batch_size`
- 减小 `imgsz`
- 启用 `amp: true`

### 2. 训练速度慢
- 增加 `workers` 数量
- 使用更快的存储设备
- 启用 `amp: true`

### 3. 精度不高
- 增加训练轮数
- 调整学习率
- 使用更大的模型
- 增加数据增强

## 许可证

本项目基于 AGPL-3.0 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！
