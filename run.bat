@echo off
chcp 65001 >nul
echo ========================================
echo           YOLO11 训练脚本
echo ========================================
echo.

:menu
echo 请选择操作:
echo 1. 安装依赖
echo 2. 快速开始向导
echo 3. 创建配置文件
echo 4. 创建数据集配置
echo 5. 开始训练
echo 6. 运行示例
echo 7. 退出
echo.
set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" goto install
if "%choice%"=="2" goto quickstart
if "%choice%"=="3" goto createconfig
if "%choice%"=="4" goto createdataset
if "%choice%"=="5" goto train
if "%choice%"=="6" goto example
if "%choice%"=="7" goto exit
echo 无效选择，请重新输入
goto menu

:install
echo.
echo 正在安装依赖...
python install.py
pause
goto menu

:quickstart
echo.
echo 启动快速开始向导...
python quick_start.py
pause
goto menu

:createconfig
echo.
echo 创建配置文件...
python main.py --mode create-config
pause
goto menu

:createdataset
echo.
echo 创建数据集配置文件...
python main.py --mode create-dataset
pause
goto menu

:train
echo.
echo 开始训练...
python main.py --mode train
pause
goto menu

:example
echo.
echo 运行训练示例...
python train_example.py
pause
goto menu

:exit
echo 再见！
pause
