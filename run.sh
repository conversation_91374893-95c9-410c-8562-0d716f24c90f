#!/bin/bash

# YOLO11 训练脚本启动器

echo "========================================"
echo "           YOLO11 训练脚本"
echo "========================================"
echo

show_menu() {
    echo "请选择操作:"
    echo "1. 安装依赖"
    echo "2. 快速开始向导"
    echo "3. 创建配置文件"
    echo "4. 创建数据集配置"
    echo "5. 开始训练"
    echo "6. 运行示例"
    echo "7. 退出"
    echo
}

while true; do
    show_menu
    read -p "请输入选择 (1-7): " choice
    
    case $choice in
        1)
            echo
            echo "正在安装依赖..."
            python3 install.py
            echo
            read -p "按回车键继续..."
            ;;
        2)
            echo
            echo "启动快速开始向导..."
            python3 quick_start.py
            echo
            read -p "按回车键继续..."
            ;;
        3)
            echo
            echo "创建配置文件..."
            python3 main.py --mode create-config
            echo
            read -p "按回车键继续..."
            ;;
        4)
            echo
            echo "创建数据集配置文件..."
            python3 main.py --mode create-dataset
            echo
            read -p "按回车键继续..."
            ;;
        5)
            echo
            echo "开始训练..."
            python3 main.py --mode train
            echo
            read -p "按回车键继续..."
            ;;
        6)
            echo
            echo "运行训练示例..."
            python3 train_example.py
            echo
            read -p "按回车键继续..."
            ;;
        7)
            echo "再见！"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done
