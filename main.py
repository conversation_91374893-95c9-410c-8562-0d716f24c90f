#!/usr/bin/env python3
"""
YOLO11 训练脚本
支持目标检测、实例分割、姿态估计、分类和旋转目标检测任务
"""

import os
import sys
import yaml
import argparse
import logging
from pathlib import Path
from datetime import datetime
from ultralytics import YOLO
import torch
import wandb
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class YOLO11Trainer:
    """YOLO11训练器类"""

    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化训练器

        Args:
            config_path: 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.model = None
        self.results = None

        # 创建输出目录
        self.output_dir = Path(self.config.get('output_dir', 'runs'))
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 设置设备
        self.device = self.config.get('device', 'auto')
        if self.device == 'auto':
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'

        logger.info(f"使用设备: {self.device}")
        logger.info(f"输出目录: {self.output_dir}")

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
            return self.get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self.get_default_config()

    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'model': {
                'name': 'yolo11n.pt',  # 可选: yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt
                'task': 'detect'  # 可选: detect, segment, classify, pose, obb
            },
            'data': {
                'path': 'coco8.yaml',  # 数据集配置文件路径
                'imgsz': 640,  # 输入图像尺寸
                'batch_size': 16,  # 批次大小
                'workers': 8  # 数据加载器工作进程数
            },
            'training': {
                'epochs': 100,  # 训练轮数
                'patience': 50,  # 早停耐心值
                'save_period': 10,  # 保存周期
                'val': True,  # 是否进行验证
                'plots': True,  # 是否生成图表
                'verbose': True,  # 详细输出
                'seed': 0,  # 随机种子
                'deterministic': True,  # 确定性训练
                'single_cls': False,  # 单类训练
                'rect': False,  # 矩形训练
                'cos_lr': False,  # 余弦学习率调度
                'close_mosaic': 10,  # 关闭马赛克增强的轮数
                'resume': False,  # 是否恢复训练
                'amp': True,  # 自动混合精度
                'fraction': 1.0,  # 数据集使用比例
                'profile': False,  # 性能分析
                'freeze': None,  # 冻结层数
                'multi_scale': False,  # 多尺度训练
                'overlap_mask': True,  # 重叠掩码
                'mask_ratio': 4,  # 掩码比例
                'dropout': 0.0,  # Dropout率
                'val_period': 1  # 验证周期
            },
            'optimizer': {
                'optimizer': 'auto',  # 优化器: SGD, Adam, AdamW, NAdam, RAdam, RMSProp, auto
                'lr0': 0.01,  # 初始学习率
                'lrf': 0.01,  # 最终学习率比例
                'momentum': 0.937,  # SGD动量/Adam beta1
                'weight_decay': 0.0005,  # 权重衰减
                'warmup_epochs': 3.0,  # 预热轮数
                'warmup_momentum': 0.8,  # 预热动量
                'warmup_bias_lr': 0.1,  # 预热偏置学习率
                'box': 7.5,  # 边界框损失增益
                'cls': 0.5,  # 分类损失增益
                'dfl': 1.5,  # 分布焦点损失增益
                'pose': 12.0,  # 姿态损失增益
                'kobj': 1.0,  # 关键点目标损失增益
                'label_smoothing': 0.0,  # 标签平滑
                'nbs': 64,  # 标称批次大小
                'hsv_h': 0.015,  # 色调增强
                'hsv_s': 0.7,  # 饱和度增强
                'hsv_v': 0.4,  # 明度增强
                'degrees': 0.0,  # 旋转增强角度
                'translate': 0.1,  # 平移增强
                'scale': 0.5,  # 缩放增强
                'shear': 0.0,  # 剪切增强
                'perspective': 0.0,  # 透视增强
                'flipud': 0.0,  # 上下翻转概率
                'fliplr': 0.5,  # 左右翻转概率
                'bgr': 0.0,  # BGR通道翻转概率
                'mosaic': 1.0,  # 马赛克增强概率
                'mixup': 0.0,  # Mixup增强概率
                'copy_paste': 0.0,  # 复制粘贴增强概率
                'auto_augment': 'randaugment',  # 自动增强策略
                'erasing': 0.4,  # 随机擦除概率
                'crop_fraction': 1.0  # 裁剪比例
            },
            'output_dir': 'runs',
            'device': 'auto',
            'project': 'yolo11_training',
            'name': None,
            'exist_ok': False,
            'pretrained': True,
            'save': True,
            'save_txt': False,
            'save_conf': False,
            'save_crop': False,
            'show': False,
            'show_conf': True,
            'show_labels': True,
            'line_width': None,
            'visualize': False,
            'augment': False,
            'agnostic_nms': False,
            'retina_masks': False,
            'embed': None,
            'show_boxes': True,
            'save_frames': False,
            'wandb': {
                'enabled': False,
                'project': 'yolo11-training',
                'name': None,
                'tags': [],
                'notes': ''
            }
        }

    def setup_wandb(self):
        """设置Weights & Biases日志记录"""
        wandb_config = self.config.get('wandb', {})
        if wandb_config.get('enabled', False):
            try:
                wandb.init(
                    project=wandb_config.get('project', 'yolo11-training'),
                    name=wandb_config.get('name'),
                    tags=wandb_config.get('tags', []),
                    notes=wandb_config.get('notes', ''),
                    config=self.config
                )
                logger.info("Weights & Biases 初始化成功")
            except Exception as e:
                logger.warning(f"Weights & Biases 初始化失败: {e}")

    def load_model(self):
        """加载YOLO11模型"""
        model_config = self.config['model']
        model_name = model_config['name']
        task = model_config['task']

        # 根据任务类型选择合适的模型
        task_models = {
            'detect': ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt', 'yolo11l.pt', 'yolo11x.pt'],
            'segment': ['yolo11n-seg.pt', 'yolo11s-seg.pt', 'yolo11m-seg.pt', 'yolo11l-seg.pt', 'yolo11x-seg.pt'],
            'classify': ['yolo11n-cls.pt', 'yolo11s-cls.pt', 'yolo11m-cls.pt', 'yolo11l-cls.pt', 'yolo11x-cls.pt'],
            'pose': ['yolo11n-pose.pt', 'yolo11s-pose.pt', 'yolo11m-pose.pt', 'yolo11l-pose.pt', 'yolo11x-pose.pt'],
            'obb': ['yolo11n-obb.pt', 'yolo11s-obb.pt', 'yolo11m-obb.pt', 'yolo11l-obb.pt', 'yolo11x-obb.pt']
        }

        # 验证模型名称和任务匹配
        if task in task_models and model_name not in task_models[task]:
            logger.warning(f"模型 {model_name} 可能不适合任务 {task}")
            # 自动选择合适的模型
            size = model_name.split('.')[0][-1] if model_name.split('.')[0][-1] in 'nsmls' else 'n'
            model_name = f"yolo11{size}{'-' + task if task != 'detect' else ''}.pt"
            logger.info(f"自动选择模型: {model_name}")

        try:
            self.model = YOLO(model_name)
            logger.info(f"成功加载模型: {model_name}")
            logger.info(f"模型任务类型: {task}")

            # 打印模型信息
            if hasattr(self.model, 'info'):
                self.model.info()

        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise

    def prepare_training_args(self) -> Dict[str, Any]:
        """准备训练参数"""
        args = {}

        # 数据配置
        data_config = self.config['data']
        args['data'] = data_config['path']
        args['imgsz'] = data_config['imgsz']
        args['batch'] = data_config['batch_size']
        args['workers'] = data_config['workers']

        # 训练配置
        training_config = self.config['training']
        args.update({
            'epochs': training_config['epochs'],
            'patience': training_config['patience'],
            'save_period': training_config['save_period'],
            'val': training_config['val'],
            'plots': training_config['plots'],
            'verbose': training_config['verbose'],
            'seed': training_config['seed'],
            'deterministic': training_config['deterministic'],
            'single_cls': training_config['single_cls'],
            'rect': training_config['rect'],
            'cos_lr': training_config['cos_lr'],
            'close_mosaic': training_config['close_mosaic'],
            'resume': training_config['resume'],
            'amp': training_config['amp'],
            'fraction': training_config['fraction'],
            'profile': training_config['profile'],
            'freeze': training_config['freeze'],
            'multi_scale': training_config['multi_scale'],
            'overlap_mask': training_config['overlap_mask'],
            'mask_ratio': training_config['mask_ratio'],
            'dropout': training_config['dropout'],
            'val_period': training_config['val_period']
        })

        # 优化器配置
        optimizer_config = self.config['optimizer']
        args.update(optimizer_config)

        # 输出配置
        args.update({
            'project': self.config.get('project', 'yolo11_training'),
            'name': self.config.get('name'),
            'exist_ok': self.config.get('exist_ok', False),
            'pretrained': self.config.get('pretrained', True),
            'save': self.config.get('save', True),
            'device': self.device
        })

        # 移除None值
        args = {k: v for k, v in args.items() if v is not None}

        return args

    def train(self):
        """开始训练"""
        logger.info("开始YOLO11训练...")

        # 设置WandB
        self.setup_wandb()

        # 加载模型
        self.load_model()

        # 准备训练参数
        train_args = self.prepare_training_args()

        logger.info("训练参数:")
        for key, value in train_args.items():
            logger.info(f"  {key}: {value}")

        try:
            # 开始训练
            self.results = self.model.train(**train_args)
            logger.info("训练完成!")

            # 保存训练结果摘要
            self.save_training_summary()

            return self.results

        except Exception as e:
            logger.error(f"训练过程中出现错误: {e}")
            raise
        finally:
            # 清理WandB
            if self.config.get('wandb', {}).get('enabled', False):
                try:
                    wandb.finish()
                except:
                    pass

    def save_training_summary(self):
        """保存训练结果摘要"""
        if self.results is None:
            return

        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'config': self.config,
                'device': self.device,
                'model_path': str(self.results.save_dir) if hasattr(self.results, 'save_dir') else None,
                'best_fitness': float(self.results.best_fitness) if hasattr(self.results, 'best_fitness') else None,
                'final_epoch': int(self.results.epoch) if hasattr(self.results, 'epoch') else None
            }

            # 保存到JSON文件
            summary_path = self.output_dir / 'training_summary.json'
            with open(summary_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(summary, f, indent=2, ensure_ascii=False)

            logger.info(f"训练摘要已保存到: {summary_path}")

        except Exception as e:
            logger.warning(f"保存训练摘要失败: {e}")

    def validate(self, data_path: Optional[str] = None):
        """验证模型"""
        if self.model is None:
            logger.error("模型未加载，请先训练或加载模型")
            return None

        try:
            val_data = data_path or self.config['data']['path']
            logger.info(f"开始验证模型，数据集: {val_data}")

            results = self.model.val(data=val_data)
            logger.info("验证完成!")

            return results

        except Exception as e:
            logger.error(f"验证过程中出现错误: {e}")
            return None

    def predict(self, source: str, save: bool = True, show: bool = False):
        """使用训练好的模型进行预测"""
        if self.model is None:
            logger.error("模型未加载，请先训练或加载模型")
            return None

        try:
            logger.info(f"开始预测，输入源: {source}")

            results = self.model.predict(
                source=source,
                save=save,
                show=show,
                conf=self.config.get('save_conf', False),
                save_txt=self.config.get('save_txt', False),
                save_crop=self.config.get('save_crop', False),
                show_conf=self.config.get('show_conf', True),
                show_labels=self.config.get('show_labels', True),
                line_width=self.config.get('line_width'),
                visualize=self.config.get('visualize', False),
                augment=self.config.get('augment', False),
                agnostic_nms=self.config.get('agnostic_nms', False),
                retina_masks=self.config.get('retina_masks', False),
                embed=self.config.get('embed'),
                show_boxes=self.config.get('show_boxes', True),
                save_frames=self.config.get('save_frames', False)
            )

            logger.info("预测完成!")
            return results

        except Exception as e:
            logger.error(f"预测过程中出现错误: {e}")
            return None

    def export_model(self, format: str = 'onnx', **kwargs):
        """导出模型到指定格式"""
        if self.model is None:
            logger.error("模型未加载，请先训练或加载模型")
            return None

        try:
            logger.info(f"开始导出模型到 {format} 格式")

            export_path = self.model.export(format=format, **kwargs)
            logger.info(f"模型导出完成: {export_path}")

            return export_path

        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            return None


def create_sample_config():
    """创建示例配置文件"""
    config = {
        'model': {
            'name': 'yolo11n.pt',
            'task': 'detect'
        },
        'data': {
            'path': 'coco8.yaml',
            'imgsz': 640,
            'batch_size': 16,
            'workers': 8
        },
        'training': {
            'epochs': 100,
            'patience': 50,
            'save_period': 10,
            'val': True,
            'plots': True,
            'verbose': True,
            'seed': 0,
            'deterministic': True,
            'amp': True,
            'resume': False
        },
        'optimizer': {
            'optimizer': 'auto',
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5
        },
        'output_dir': 'runs',
        'device': 'auto',
        'project': 'yolo11_training',
        'wandb': {
            'enabled': False,
            'project': 'yolo11-training'
        }
    }

    with open('config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

    print("示例配置文件 'config.yaml' 已创建")


def create_sample_dataset_config():
    """创建示例数据集配置文件"""
    dataset_config = {
        'path': './dataset',  # 数据集根目录
        'train': 'images/train',  # 训练图像目录
        'val': 'images/val',  # 验证图像目录
        'test': 'images/test',  # 测试图像目录（可选）

        'nc': 80,  # 类别数量
        'names': [  # 类别名称列表
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
    }

    with open('dataset.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)

    print("示例数据集配置文件 'dataset.yaml' 已创建")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='YOLO11 训练脚本')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--mode', type=str, default='train',
                       choices=['train', 'val', 'predict', 'export', 'create-config', 'create-dataset'],
                       help='运行模式')
    parser.add_argument('--source', type=str, help='预测时的输入源（图像/视频/目录路径）')
    parser.add_argument('--model', type=str, help='模型路径（用于预测/验证/导出）')
    parser.add_argument('--format', type=str, default='onnx', help='导出格式')
    parser.add_argument('--data', type=str, help='数据集配置文件路径')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch-size', type=int, help='批次大小')
    parser.add_argument('--imgsz', type=int, help='输入图像尺寸')
    parser.add_argument('--device', type=str, help='设备 (cpu, cuda, auto)')
    parser.add_argument('--project', type=str, help='项目名称')
    parser.add_argument('--name', type=str, help='实验名称')
    parser.add_argument('--resume', action='store_true', help='恢复训练')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.mode == 'create-config':
        create_sample_config()
        return

    if args.mode == 'create-dataset':
        create_sample_dataset_config()
        return

    # 创建训练器
    trainer = YOLO11Trainer(args.config)

    # 命令行参数覆盖配置文件
    if args.data:
        trainer.config['data']['path'] = args.data
    if args.epochs:
        trainer.config['training']['epochs'] = args.epochs
    if args.batch_size:
        trainer.config['data']['batch_size'] = args.batch_size
    if args.imgsz:
        trainer.config['data']['imgsz'] = args.imgsz
    if args.device:
        trainer.config['device'] = args.device
        trainer.device = args.device
    if args.project:
        trainer.config['project'] = args.project
    if args.name:
        trainer.config['name'] = args.name
    if args.resume:
        trainer.config['training']['resume'] = True
    if args.verbose:
        trainer.config['training']['verbose'] = True

    try:
        if args.mode == 'train':
            logger.info("开始训练模式")
            results = trainer.train()
            logger.info("训练完成")

        elif args.mode == 'val':
            logger.info("开始验证模式")
            if args.model:
                trainer.model = YOLO(args.model)
            results = trainer.validate(args.data)
            logger.info("验证完成")

        elif args.mode == 'predict':
            logger.info("开始预测模式")
            if not args.source:
                logger.error("预测模式需要指定 --source 参数")
                return
            if args.model:
                trainer.model = YOLO(args.model)
            else:
                logger.error("预测模式需要指定 --model 参数")
                return
            results = trainer.predict(args.source)
            logger.info("预测完成")

        elif args.mode == 'export':
            logger.info("开始导出模式")
            if args.model:
                trainer.model = YOLO(args.model)
            else:
                logger.error("导出模式需要指定 --model 参数")
                return
            export_path = trainer.export_model(args.format)
            logger.info(f"模型已导出到: {export_path}")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"运行过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    main()