#!/usr/bin/env python3
"""
YOLO11 训练示例脚本
演示如何使用训练器进行不同任务的训练
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from main import YOLO11Trainer
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_detection_training():
    """目标检测训练示例"""
    logger.info("=== 目标检测训练示例 ===")
    
    # 创建自定义配置
    config = {
        'model': {
            'name': 'yolo11n.pt',
            'task': 'detect'
        },
        'data': {
            'path': 'coco8.yaml',  # 使用COCO8示例数据集
            'imgsz': 640,
            'batch_size': 8,  # 较小的批次大小适合演示
            'workers': 4
        },
        'training': {
            'epochs': 10,  # 较少的轮数用于演示
            'patience': 5,
            'val': True,
            'plots': True,
            'verbose': True,
            'amp': True
        },
        'optimizer': {
            'lr0': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005
        },
        'device': 'auto',
        'project': 'yolo11_detection_demo',
        'name': 'detection_example'
    }
    
    # 保存配置到临时文件
    import yaml
    with open('temp_detection_config.yaml', 'w') as f:
        yaml.dump(config, f)
    
    try:
        # 创建训练器并开始训练
        trainer = YOLO11Trainer('temp_detection_config.yaml')
        results = trainer.train()
        
        logger.info("目标检测训练完成!")
        
        # 进行验证
        val_results = trainer.validate()
        logger.info("验证完成!")
        
        return results
        
    finally:
        # 清理临时文件
        if os.path.exists('temp_detection_config.yaml'):
            os.remove('temp_detection_config.yaml')


def example_segmentation_training():
    """实例分割训练示例"""
    logger.info("=== 实例分割训练示例 ===")
    
    config = {
        'model': {
            'name': 'yolo11n-seg.pt',
            'task': 'segment'
        },
        'data': {
            'path': 'coco8-seg.yaml',
            'imgsz': 640,
            'batch_size': 4,  # 分割任务需要更多内存
            'workers': 4
        },
        'training': {
            'epochs': 10,
            'patience': 5,
            'val': True,
            'plots': True,
            'amp': True
        },
        'device': 'auto',
        'project': 'yolo11_segmentation_demo',
        'name': 'segmentation_example'
    }
    
    import yaml
    with open('temp_seg_config.yaml', 'w') as f:
        yaml.dump(config, f)
    
    try:
        trainer = YOLO11Trainer('temp_seg_config.yaml')
        results = trainer.train()
        
        logger.info("实例分割训练完成!")
        return results
        
    finally:
        if os.path.exists('temp_seg_config.yaml'):
            os.remove('temp_seg_config.yaml')


def example_classification_training():
    """图像分类训练示例"""
    logger.info("=== 图像分类训练示例 ===")
    
    config = {
        'model': {
            'name': 'yolo11n-cls.pt',
            'task': 'classify'
        },
        'data': {
            'path': 'imagenet10',  # 使用ImageNet10示例数据集
            'imgsz': 224,  # 分类任务通常使用224x224
            'batch_size': 16,
            'workers': 4
        },
        'training': {
            'epochs': 10,
            'patience': 5,
            'val': True,
            'plots': True,
            'amp': True
        },
        'device': 'auto',
        'project': 'yolo11_classification_demo',
        'name': 'classification_example'
    }
    
    import yaml
    with open('temp_cls_config.yaml', 'w') as f:
        yaml.dump(config, f)
    
    try:
        trainer = YOLO11Trainer('temp_cls_config.yaml')
        results = trainer.train()
        
        logger.info("图像分类训练完成!")
        return results
        
    finally:
        if os.path.exists('temp_cls_config.yaml'):
            os.remove('temp_cls_config.yaml')


def example_pose_training():
    """姿态估计训练示例"""
    logger.info("=== 姿态估计训练示例 ===")
    
    config = {
        'model': {
            'name': 'yolo11n-pose.pt',
            'task': 'pose'
        },
        'data': {
            'path': 'coco8-pose.yaml',
            'imgsz': 640,
            'batch_size': 8,
            'workers': 4
        },
        'training': {
            'epochs': 10,
            'patience': 5,
            'val': True,
            'plots': True,
            'amp': True
        },
        'device': 'auto',
        'project': 'yolo11_pose_demo',
        'name': 'pose_example'
    }
    
    import yaml
    with open('temp_pose_config.yaml', 'w') as f:
        yaml.dump(config, f)
    
    try:
        trainer = YOLO11Trainer('temp_pose_config.yaml')
        results = trainer.train()
        
        logger.info("姿态估计训练完成!")
        return results
        
    finally:
        if os.path.exists('temp_pose_config.yaml'):
            os.remove('temp_pose_config.yaml')


def example_prediction():
    """预测示例"""
    logger.info("=== 预测示例 ===")
    
    # 使用训练好的模型进行预测
    from ultralytics import YOLO
    
    # 加载预训练模型
    model = YOLO('yolo11n.pt')
    
    # 预测示例图像（如果存在）
    test_images = [
        'https://ultralytics.com/images/bus.jpg',  # 在线图像
        'https://ultralytics.com/images/zidane.jpg'
    ]
    
    for source in test_images:
        try:
            logger.info(f"预测图像: {source}")
            results = model.predict(source, save=True, conf=0.25)
            
            # 打印结果
            for r in results:
                logger.info(f"检测到 {len(r.boxes)} 个目标")
                
        except Exception as e:
            logger.warning(f"预测失败: {e}")


def example_model_export():
    """模型导出示例"""
    logger.info("=== 模型导出示例 ===")
    
    from ultralytics import YOLO
    
    # 加载模型
    model = YOLO('yolo11n.pt')
    
    # 导出到不同格式
    export_formats = ['onnx', 'torchscript']
    
    for fmt in export_formats:
        try:
            logger.info(f"导出模型到 {fmt} 格式")
            export_path = model.export(format=fmt)
            logger.info(f"导出成功: {export_path}")
            
        except Exception as e:
            logger.warning(f"导出到 {fmt} 失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    logger.info("开始YOLO11训练示例演示")
    
    examples = [
        ("目标检测", example_detection_training),
        ("预测", example_prediction),
        ("模型导出", example_model_export),
        # 注释掉其他示例以节省时间，需要时可以取消注释
        # ("实例分割", example_segmentation_training),
        # ("图像分类", example_classification_training),
        # ("姿态估计", example_pose_training),
    ]
    
    for name, func in examples:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"运行示例: {name}")
            logger.info(f"{'='*50}")
            
            func()
            
            logger.info(f"✓ {name} 示例完成")
            
        except KeyboardInterrupt:
            logger.info("用户中断操作")
            break
        except Exception as e:
            logger.error(f"✗ {name} 示例失败: {e}")
            continue
    
    logger.info("\n所有示例演示完成!")


if __name__ == "__main__":
    main()
