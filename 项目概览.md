# YOLO11 训练脚本项目概览

## 📁 文件结构

```
yolo11-training/
├── main.py              # 主训练脚本
├── config.yaml          # 配置文件模板
├── dataset.yaml         # 数据集配置模板
├── utils.py             # 工具函数
├── requirements.txt     # 依赖包列表
├── install.py           # 自动安装脚本
├── quick_start.py       # 快速开始向导
├── train_example.py     # 训练示例
├── run.bat             # Windows启动脚本
├── run.sh              # Linux/Mac启动脚本
├── README.md           # 详细说明文档
└── 项目概览.md         # 本文件
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行中执行
run.bat
```

**Linux/Mac用户：**
```bash
# 给脚本添加执行权限
chmod +x run.sh
# 运行脚本
./run.sh
```

### 方法二：使用快速开始向导

```bash
python quick_start.py
```

### 方法三：手动步骤

1. **安装依赖**
   ```bash
   python install.py
   ```

2. **创建配置文件**
   ```bash
   python main.py --mode create-config
   ```

3. **开始训练**
   ```bash
   python main.py --mode train
   ```

## 📋 核心文件说明

### 🎯 main.py - 主训练脚本
- **功能**: 完整的YOLO11训练器类
- **支持任务**: 检测、分割、分类、姿态估计、旋转目标检测
- **特性**: 
  - 灵活的配置系统
  - WandB集成
  - 自动模型选择
  - 完整的命令行接口

### ⚙️ config.yaml - 配置文件
- **用途**: 训练参数配置
- **包含**: 模型设置、数据配置、训练参数、优化器设置
- **特点**: 详细的中文注释

### 📊 dataset.yaml - 数据集配置
- **用途**: 数据集路径和类别定义
- **格式**: YOLO标准格式
- **示例**: 包含COCO 80类的完整配置

### 🛠️ utils.py - 工具函数
- **功能**: 
  - 数据集结构检查
  - 文件统计和验证
  - 标注可视化
  - 数据集分割
  - 报告生成

### 🔧 install.py - 自动安装脚本
- **功能**: 
  - 检查Python版本
  - 自动检测GPU
  - 安装PyTorch和依赖
  - 验证安装

### 🎮 quick_start.py - 快速开始向导
- **功能**: 
  - 交互式配置生成
  - 任务类型选择
  - 参数设置向导
  - 一键开始训练

### 📚 train_example.py - 训练示例
- **功能**: 
  - 不同任务的训练示例
  - 预测示例
  - 模型导出示例

## 🎯 支持的任务类型

| 任务类型 | 模型文件 | 说明 |
|---------|---------|------|
| 目标检测 | yolo11n/s/m/l/x.pt | 检测图像中的目标并定位 |
| 实例分割 | yolo11n/s/m/l/x-seg.pt | 检测目标并分割像素级掩码 |
| 图像分类 | yolo11n/s/m/l/x-cls.pt | 对整张图像进行分类 |
| 姿态估计 | yolo11n/s/m/l/x-pose.pt | 检测人体关键点 |
| 旋转目标检测 | yolo11n/s/m/l/x-obb.pt | 检测任意角度的目标 |

## 📈 模型大小对比

| 模型 | 参数量 | 速度 | 精度 | 适用场景 |
|------|--------|------|------|----------|
| n (Nano) | 最少 | 最快 | 较低 | 移动设备、实时应用 |
| s (Small) | 少 | 快 | 中等 | 边缘设备 |
| m (Medium) | 中等 | 中等 | 较高 | 平衡性能 |
| l (Large) | 多 | 慢 | 高 | 高精度需求 |
| x (Extra Large) | 最多 | 最慢 | 最高 | 最高精度需求 |

## 🔄 使用流程

```mermaid
graph TD
    A[开始] --> B[安装依赖]
    B --> C[准备数据集]
    C --> D[选择任务类型]
    D --> E[配置参数]
    E --> F[开始训练]
    F --> G[验证模型]
    G --> H[导出模型]
    H --> I[部署使用]
```

## 📝 常用命令

### 训练相关
```bash
# 基础训练
python main.py --mode train

# 指定参数训练
python main.py --mode train --data dataset.yaml --epochs 100 --batch-size 16

# 恢复训练
python main.py --mode train --resume

# 多GPU训练
python main.py --mode train --device 0,1,2,3
```

### 验证和预测
```bash
# 验证模型
python main.py --mode val --model best.pt --data dataset.yaml

# 预测图像
python main.py --mode predict --model best.pt --source image.jpg

# 预测视频
python main.py --mode predict --model best.pt --source video.mp4
```

### 模型导出
```bash
# 导出ONNX
python main.py --mode export --model best.pt --format onnx

# 导出TensorRT
python main.py --mode export --model best.pt --format engine
```

## 🎨 数据集格式

### 目录结构
```
dataset/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像（可选）
└── labels/
    ├── train/          # 训练标签
    ├── val/            # 验证标签
    └── test/           # 测试标签（可选）
```

### 标签格式
每个图像对应一个同名的.txt文件，每行一个目标：
```
class_id center_x center_y width height
```
所有坐标值都是相对于图像尺寸的归一化值(0-1)。

## 🔧 故障排除

### 常见问题

1. **内存不足**
   - 减小batch_size
   - 减小图像尺寸
   - 启用混合精度训练(amp: true)

2. **训练速度慢**
   - 增加workers数量
   - 使用SSD存储
   - 启用混合精度训练

3. **精度不高**
   - 增加训练轮数
   - 使用更大的模型
   - 调整学习率
   - 增加数据增强

4. **CUDA错误**
   - 检查GPU内存
   - 更新CUDA驱动
   - 降低batch_size

## 📞 获取帮助

- 查看README.md获取详细文档
- 运行quick_start.py获取交互式帮助
- 检查train_example.py查看使用示例
- 使用utils.py中的工具函数验证数据集

## 🎉 开始使用

选择最适合您的方式开始：

1. **新手用户**: 运行 `python quick_start.py`
2. **有经验用户**: 直接编辑 `config.yaml` 然后运行 `python main.py`
3. **开发者**: 查看 `train_example.py` 了解API使用

祝您训练愉快！🚀
