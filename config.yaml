data:
  batch_size: 16
  imgsz: 640
  path: coco8.yaml
  workers: 8
device: auto
model:
  name: yolo11n.pt
  task: detect
optimizer:
  box: 7.5
  cls: 0.5
  dfl: 1.5
  lr0: 0.01
  lrf: 0.01
  momentum: 0.937
  optimizer: auto
  warmup_epochs: 3.0
  weight_decay: 0.0005
output_dir: runs
project: yolo11_training
training:
  amp: true
  deterministic: true
  epochs: 100
  patience: 50
  plots: true
  resume: false
  save_period: 10
  seed: 0
  val: true
  verbose: true
wandb:
  enabled: false
  project: yolo11-training
